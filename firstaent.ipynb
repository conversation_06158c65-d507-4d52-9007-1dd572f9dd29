{"cells": [{"cell_type": "code", "execution_count": 38, "id": "8d198325", "metadata": {}, "outputs": [{"data": {"text/plain": ["True"]}, "execution_count": 38, "metadata": {}, "output_type": "execute_result"}], "source": ["from dotenv import load_dotenv\n", "load_dotenv(override=True)"]}, {"cell_type": "code", "execution_count": 5, "id": "2398a647", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Collecting openai\n", "  Obtaining dependency information for openai from https://files.pythonhosted.org/packages/e8/fb/df274ca10698ee77b07bff952f302ea627cc12dac6b85289485dd77db6de/openai-1.99.9-py3-none-any.whl.metadata\n", "  Downloading openai-1.99.9-py3-none-any.whl.metadata (29 kB)\n", "Requirement already satisfied: anyio<5,>=3.5.0 in c:\\users\\<USER>\\anaconda3\\lib\\site-packages (from openai) (4.9.0)\n", "Requirement already satisfied: distro<2,>=1.7.0 in c:\\users\\<USER>\\anaconda3\\lib\\site-packages (from openai) (1.9.0)\n", "Requirement already satisfied: httpx<1,>=0.23.0 in c:\\users\\<USER>\\anaconda3\\lib\\site-packages (from openai) (0.28.1)\n", "Collecting jiter<1,>=0.4.0 (from openai)\n", "  Obtaining dependency information for jiter<1,>=0.4.0 from https://files.pythonhosted.org/packages/c2/c9/d394706deb4c660137caf13e33d05a031d734eb99c051142e039d8ceb794/jiter-0.10.0-cp311-cp311-win_amd64.whl.metadata\n", "  Downloading jiter-0.10.0-cp311-cp311-win_amd64.whl.metadata (5.3 kB)\n", "Requirement already satisfied: pydantic<3,>=1.9.0 in c:\\users\\<USER>\\anaconda3\\lib\\site-packages (from openai) (2.11.3)\n", "Requirement already satisfied: sniffio in c:\\users\\<USER>\\anaconda3\\lib\\site-packages (from openai) (1.2.0)\n", "Requirement already satisfied: tqdm>4 in c:\\users\\<USER>\\anaconda3\\lib\\site-packages (from openai) (4.67.1)\n", "Requirement already satisfied: typing-extensions<5,>=4.11 in c:\\users\\<USER>\\anaconda3\\lib\\site-packages (from openai) (4.12.2)\n", "Requirement already satisfied: idna>=2.8 in c:\\users\\<USER>\\anaconda3\\lib\\site-packages (from anyio<5,>=3.5.0->openai) (3.4)\n", "Requirement already satisfied: certifi in c:\\users\\<USER>\\anaconda3\\lib\\site-packages (from httpx<1,>=0.23.0->openai) (2023.7.22)\n", "Requirement already satisfied: httpcore==1.* in c:\\users\\<USER>\\anaconda3\\lib\\site-packages (from httpx<1,>=0.23.0->openai) (1.0.8)\n", "Requirement already satisfied: h11<0.15,>=0.13 in c:\\users\\<USER>\\anaconda3\\lib\\site-packages (from httpcore==1.*->httpx<1,>=0.23.0->openai) (0.14.0)\n", "Requirement already satisfied: annotated-types>=0.6.0 in c:\\users\\<USER>\\anaconda3\\lib\\site-packages (from pydantic<3,>=1.9.0->openai) (0.7.0)\n", "Requirement already satisfied: pydantic-core==2.33.1 in c:\\users\\<USER>\\anaconda3\\lib\\site-packages (from pydantic<3,>=1.9.0->openai) (2.33.1)\n", "Requirement already satisfied: typing-inspection>=0.4.0 in c:\\users\\<USER>\\anaconda3\\lib\\site-packages (from pydantic<3,>=1.9.0->openai) (0.4.0)\n", "Requirement already satisfied: colorama in c:\\users\\<USER>\\anaconda3\\lib\\site-packages (from tqdm>4->openai) (0.4.6)\n", "Downloading openai-1.99.9-py3-none-any.whl (786 kB)\n", "   ---------------------------------------- 0.0/786.8 kB ? eta -:--:--\n", "    --------------------------------------- 10.2/786.8 kB ? eta -:--:--\n", "   -- ------------------------------------ 41.0/786.8 kB 487.6 kB/s eta 0:00:02\n", "   ---- ---------------------------------- 92.2/786.8 kB 744.7 kB/s eta 0:00:01\n", "   ----- -------------------------------- 122.9/786.8 kB 654.9 kB/s eta 0:00:02\n", "   -------- ----------------------------- 174.1/786.8 kB 871.5 kB/s eta 0:00:01\n", "   ---------------- ----------------------- 327.7/786.8 kB 1.2 MB/s eta 0:00:01\n", "   ----------------------------------- ---- 696.3/786.8 kB 2.2 MB/s eta 0:00:01\n", "   ---------------------------------------- 786.8/786.8 kB 2.2 MB/s eta 0:00:00\n", "Downloading jiter-0.10.0-cp311-cp311-win_amd64.whl (209 kB)\n", "   ---------------------------------------- 0.0/209.2 kB ? eta -:--:--\n", "   --------------------------------------- 209.2/209.2 kB 12.4 MB/s eta 0:00:00\n", "Installing collected packages: jiter, openai\n", "Successfully installed jiter-0.10.0 openai-1.99.9\n"]}], "source": ["!pip install openai"]}, {"cell_type": "code", "execution_count": 39, "id": "1daf2903", "metadata": {}, "outputs": [{"data": {"text/markdown": ["A farmer has a 17-meter-long fence and wants to enclose the largest possible rectangular area against a straight riverbank, using the river as one side of the rectangle and fencing for the other three sides. What dimensions should the farmer choose to maximize the enclosed area, and what is that maximum area?"], "text/plain": ["<IPython.core.display.Markdown object>"]}, "execution_count": 39, "metadata": {}, "output_type": "execute_result"}], "source": ["from openai import OpenAI\n", "client = OpenAI()\n", "\n", "\n", "question = \"Please propose a hard, challenging question to assess someone's IQ. Respond only with the question.\"\n", "\n", "messages = [{\"role\": \"user\", \"content\": question}]\n", "response = client.chat.completions.create(model=\"gpt-4.1-nano\", messages=messages)\n", "\n", "#display as markdown\n", "from IPython.display import Markdown\n", "Markdown(response.choices[0].message.content)\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": 40, "id": "1fa7cf28", "metadata": {}, "outputs": [], "source": ["request = \"Please come up with a challenging, nuanced question that I can ask a number of LLMs to evaluate their intelligence. \"\n", "request += \"Answer only with the question, no explanation.\"\n", "messages = [{\"role\": \"user\", \"content\": request}]\n"]}, {"cell_type": "code", "execution_count": 41, "id": "f15a70f7", "metadata": {}, "outputs": [{"data": {"text/plain": ["[{'role': 'user',\n", "  'content': 'Please come up with a challenging, nuanced question that I can ask a number of LLMs to evaluate their intelligence. Answer only with the question, no explanation.'}]"]}, "execution_count": 41, "metadata": {}, "output_type": "execute_result"}], "source": ["messages"]}, {"cell_type": "code", "execution_count": 9, "id": "f34b5822", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Collecting groq\n", "  Obtaining dependency information for groq from https://files.pythonhosted.org/packages/ab/f8/14672d69a91495f43462c5490067eeafc30346e81bda1a62848e897f9bc3/groq-0.31.0-py3-none-any.whl.metadata\n", "  Downloading groq-0.31.0-py3-none-any.whl.metadata (16 kB)\n", "Requirement already satisfied: anyio<5,>=3.5.0 in c:\\users\\<USER>\\anaconda3\\lib\\site-packages (from groq) (4.9.0)\n", "Requirement already satisfied: distro<2,>=1.7.0 in c:\\users\\<USER>\\anaconda3\\lib\\site-packages (from groq) (1.9.0)\n", "Requirement already satisfied: httpx<1,>=0.23.0 in c:\\users\\<USER>\\anaconda3\\lib\\site-packages (from groq) (0.28.1)\n", "Requirement already satisfied: pydantic<3,>=1.9.0 in c:\\users\\<USER>\\anaconda3\\lib\\site-packages (from groq) (2.11.3)\n", "Requirement already satisfied: sniffio in c:\\users\\<USER>\\anaconda3\\lib\\site-packages (from groq) (1.2.0)\n", "Requirement already satisfied: typing-extensions<5,>=4.10 in c:\\users\\<USER>\\anaconda3\\lib\\site-packages (from groq) (4.12.2)\n", "Requirement already satisfied: idna>=2.8 in c:\\users\\<USER>\\anaconda3\\lib\\site-packages (from anyio<5,>=3.5.0->groq) (3.4)\n", "Requirement already satisfied: certifi in c:\\users\\<USER>\\anaconda3\\lib\\site-packages (from httpx<1,>=0.23.0->groq) (2023.7.22)\n", "Requirement already satisfied: httpcore==1.* in c:\\users\\<USER>\\anaconda3\\lib\\site-packages (from httpx<1,>=0.23.0->groq) (1.0.8)\n", "Requirement already satisfied: h11<0.15,>=0.13 in c:\\users\\<USER>\\anaconda3\\lib\\site-packages (from httpcore==1.*->httpx<1,>=0.23.0->groq) (0.14.0)\n", "Requirement already satisfied: annotated-types>=0.6.0 in c:\\users\\<USER>\\anaconda3\\lib\\site-packages (from pydantic<3,>=1.9.0->groq) (0.7.0)\n", "Requirement already satisfied: pydantic-core==2.33.1 in c:\\users\\<USER>\\anaconda3\\lib\\site-packages (from pydantic<3,>=1.9.0->groq) (2.33.1)\n", "Requirement already satisfied: typing-inspection>=0.4.0 in c:\\users\\<USER>\\anaconda3\\lib\\site-packages (from pydantic<3,>=1.9.0->groq) (0.4.0)\n", "Downloading groq-0.31.0-py3-none-any.whl (131 kB)\n", "   ---------------------------------------- 0.0/131.4 kB ? eta -:--:--\n", "   --- ------------------------------------ 10.2/131.4 kB ? eta -:--:--\n", "   --------- ----------------------------- 30.7/131.4 kB 330.3 kB/s eta 0:00:01\n", "   ------------ -------------------------- 41.0/131.4 kB 219.4 kB/s eta 0:00:01\n", "   ------------------ -------------------- 61.4/131.4 kB 328.2 kB/s eta 0:00:01\n", "   --------------------------- ----------- 92.2/131.4 kB 374.1 kB/s eta 0:00:01\n", "   ----------------------------------- -- 122.9/131.4 kB 425.1 kB/s eta 0:00:01\n", "   -------------------------------------- 131.4/131.4 kB 388.2 kB/s eta 0:00:00\n", "Installing collected packages: groq\n", "Successfully installed groq-0.31.0\n"]}], "source": ["!pip install groq\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "532fc137", "metadata": {}, "outputs": [{"data": {"text/markdown": ["Can a hypothetical artificial general intelligence, capable of self-modifying its own architecture and objectives, ever truly be said to have a fixed or coherent sense of identity, and if not, what implications might this have for our understanding of autonomy, free will, and moral responsibility in machines?"], "text/plain": ["<IPython.core.display.Markdown object>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["import os\n", "client = OpenAI(\n", "    api_key=os.getenv('GROQ_API_KEY'),\n", "    base_url=\"https://api.groq.com/openai/v1\"\n", ")\n", "\n", "model_name = \"llama-3.3-70b-versatile\"\n", "\n", "response = client.chat.completions.create(model=model_name, messages=messages)\n", "\n", "answer = response.choices[0].message.content\n", "\n", "display(<PERSON><PERSON>(answer))\n", "\n"]}, {"cell_type": "code", "execution_count": 18, "id": "66fbbbe6", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Collecting pypdf2\n", "  Obtaining dependency information for pypdf2 from https://files.pythonhosted.org/packages/8e/5e/c86a5643653825d3c913719e788e41386bee415c2b87b4f955432f2de6b2/pypdf2-3.0.1-py3-none-any.whl.metadata\n", "  Downloading pypdf2-3.0.1-py3-none-any.whl.metadata (6.8 kB)\n", "Downloading pypdf2-3.0.1-py3-none-any.whl (232 kB)\n", "   ---------------------------------------- 0.0/232.6 kB ? eta -:--:--\n", "   - -------------------------------------- 10.2/232.6 kB ? eta -:--:--\n", "   ----- --------------------------------- 30.7/232.6 kB 262.6 kB/s eta 0:00:01\n", "   ----- --------------------------------- 30.7/232.6 kB 262.6 kB/s eta 0:00:01\n", "   ----- --------------------------------- 30.7/232.6 kB 262.6 kB/s eta 0:00:01\n", "   ---------- ---------------------------- 61.4/232.6 kB 273.8 kB/s eta 0:00:01\n", "   ---------- ---------------------------- 61.4/232.6 kB 273.8 kB/s eta 0:00:01\n", "   --------------- ----------------------- 92.2/232.6 kB 276.8 kB/s eta 0:00:01\n", "   -------------------- ----------------- 122.9/232.6 kB 313.8 kB/s eta 0:00:01\n", "   ------------------------------- ------ 194.6/232.6 kB 454.0 kB/s eta 0:00:01\n", "   ------------------------------------ - 225.3/232.6 kB 510.8 kB/s eta 0:00:01\n", "   -------------------------------------- 232.6/232.6 kB 490.8 kB/s eta 0:00:00\n", "Installing collected packages: pypdf2\n", "Successfully installed pypdf2-3.0.1\n"]}], "source": ["#read pdf us9ng pdfreader\n", "\n", "!pip install pypdf2\n", "\n"]}, {"cell_type": "code", "execution_count": 42, "id": "d787de34", "metadata": {}, "outputs": [], "source": ["import PyPDF2\n", "import os\n", "\n", "def read_pdf(file_path):\n", "    \"\"\"Read PDF file and extract text from all pages\"\"\"\n", "    text = \"\"\n", "    \n", "    with open(file_path, 'rb') as file:\n", "        pdf_reader = PyPDF2.PdfReader(file)\n", "        \n", "        # Get number of pages\n", "        num_pages = len(pdf_reader.pages)\n", "        print(f\"PDF has {num_pages} pages\")\n", "        \n", "        # Extract text from each page\n", "        for page_num in range(num_pages):\n", "            page = pdf_reader.pages[page_num]\n", "            text += page.extract_text() + \"\\n\"\n", "    \n", "    return text\n", "\n"]}, {"cell_type": "code", "execution_count": 43, "id": "d1bc5bf5", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["PDF has 3 pages\n", "   \n", "Contact\n", "5140 prancing pass cumming ga\n", "**********  (Mobile)\n", "<EMAIL>\n", "www.linkedin.com/in/tushar-\n", "manna-3aa31810  (LinkedIn)\n", "Top Skills\n", "Software Development Life Cycle\n", "(SDLC)\n", "SQL\n", "Software DevelopmentTushar Manna\n", "Senior Software Engg at Lyric\n", "Alpharetta, Georgia, United States\n", "Summary\n", "Accomplished IT professional with over 18 years+ experience in\n", "Healthcare IT domain,,  Excel at leading cross functional teams such\n", "as Requirements Team/Development and QA, providing strategic\n", "and tactical direction and deciding upon applicable architectural\n", "strategies for successful deliverables.  Savvy with technology\n", "including most ASP.Net, WCF, C #.NET , C++, VC++ (MFC), STL,\n", "COM/DCOM (ATL),Angular JS, TypeScript,Node JS Python ,Tibco\n", "Spotfire , SQL Server,Oracle, Visual Studio 2017, Net MVC / Web\n", "API, REST, OOP class design\n", "Experience\n", "Lyric - Clarity in motion.\n", "Sr Software engineer\n", "October 2023 - Present  (1 year 11 months)\n", "Skills:Lifecycle Management · Communication · Scrum · Computer Science\n", "· Secure File Transfer Protocol (SFTP) · Interpersonal Communication ·\n", "Information Technology · Performance Reviews · Data Privacy · Software\n", "Development Methodologies · Business Requirements · Coaching · Analytical\n", "Skills · Leadership · TypeScript · Technical Engineering · Enterprise Software\n", "· Software Architecture · Secure Shell (SSH) · HTML · Microsoft SQL Server ·\n", "Oracle- Problem Solving · Strategic Thinking\n", "Change Healthcare\n", "Senior Software Engineer\n", "February 2017 - February 2024  (7 years 1 month)\n", "alpharetta \n", "Skills:Lifecycle Management · Communication · Scrum · Computer Science\n", "· Secure File Transfer Protocol (SFTP) · Interpersonal Communication ·\n", "Information Technology · Performance Reviews · Data Privacy · Software\n", "Development Methodologies · Business Requirements · Coaching · Analytical\n", "Skills · Leadership · TypeScript · Technical Engineering · Enterprise Software\n", "  Page 1 of 3\n", "   \n", "· Software Architecture · Secure Shell (SSH) · HTML · Microsoft SQL Server ·\n", "Oracle- Problem Solving · Strategic Thinking\n", "<PERSON><PERSON><PERSON><PERSON><PERSON>\n", "Senior Software Engg\n", "December 2003 - January 2017  (13 years 2 months)\n", "Skills:Lifecycle Management · Communication · Scrum · Computer Science\n", "· Secure File Transfer Protocol (SFTP) · Interpersonal Communication ·\n", "Information Technology · Performance Reviews · Data Privacy · Software\n", "Development Methodologies · Business Requirements · Coaching · Analytical\n", "Skills · Leadership · TypeScript · Technical Engineering · Enterprise Software\n", "· Software Architecture · Secure Shell (SSH) · HTML · Microsoft SQL Server ·\n", "Oracle- Problem Solving · Strategic Thinking\n", "JPMorgan Chase & Co.\n", "Software engg\n", "April 2006 - January 2007  (10 months)\n", "Dover, Delaware Area\n", "Skills: Management Professional · Skill Development · Identity & Access\n", "Management (IAM) · Application Lifecycle Management · Communication\n", "·  Computer Science · Secure File Transfer Protocol (SFTP) · Interpersonal\n", "Communication · Information Technology · Performance Reviews · Data\n", "Privacy · Software Development Methodologies · Business Requirements ·\n", "Coaching · Analytical Skills · Leadership · TypeScript · Technical Engineering\n", "· Enterprise Software · Software Architecture · Secure Shell (SSH) · HTML ·\n", "Microsoft SQL Server ·Oracle-  Problem Solving · Strategic Thinking\n", "SYNTELL Inc.\n", "Project Lead\n", "July 2003 - March 2006  (2 years 9 months)\n", "Skills: Skill Development · Identity & Access Management (IAM) · Application\n", "Lifecycle Management · Communication ·  Computer Science · Secure\n", "File Transfer Protocol (SFTP) · Interpersonal Communication · Information\n", "Technology ·  Performance Reviews · Software Development Methodologies ·\n", "Business Requirements · Coaching · Analytical Skills · Leadership · TypeScript\n", "· Technical Engineering · Enterprise Software · Software Architecture · Secure\n", "Shell (SSH) · HTML · Microsoft SQL Server · Oracle- Problem Solving ·\n", "Strategic Thinking\n", "KPIT Cummins Infosystems Limited\n", "  Page 2 of 3\n", "   \n", "Systems Executive\n", "December 2002 - June 2003  (7 months)\n", "Skills: Skill Development · Application Lifecycle Management · Communication\n", "· Computer Science · Secure File Transfer Protocol (SFTP) · Interpersonal\n", "Communication · Performance Reviews · Data Privacy · Software\n", "Development Methodologies · Business Requirements · Coaching · Analytical\n", "Skills · Leadership · TypeScript · Technical Engineering · Enterprise Software\n", "· Software Architecture · HTML · Microsoft SQL Server · Oracle - Problem\n", "Solving\n", "Knights Technology\n", "Sr Software Engg\n", "2000 - 2002  (2 years)\n", "Skills: Skill Development · Application Lifecycle Management · Communication\n", "·  Computer Science · Interpersonal Communication · Performance Reviews\n", "·  Software Development Methodologies · Business Requirements · Coaching\n", "· Analytical Skills · Leadership · Technical Engineering · Enterprise Software ·\n", "Software Architecture · HTML · Problem Solving\n", "Knights\n", "Software Engg\n", "2000 - 2002  (2 years)\n", "Skills: Six Sigma · Application Lifecycle Management · Computer Science\n", "· Interpersonal Communication · · Business Requirements · Coaching ·\n", "Analytical Skills · Secure Shell (SSH) · Microsoft SQL Server · Problem Solving\n", "Education\n", "University of Mumbai\n", "BE, Production  · (1995 - 1999)\n", "K.J.Somaiya College of Engg\n", "BE, Production  · (1995 - 1999)\n", "  Page 3 of 3\n", "\n"]}], "source": ["# Example usage\n", "linkedin = read_pdf(\"profile.pdf\")\n", "print(linkedin)"]}, {"cell_type": "code", "execution_count": 44, "id": "4086a935", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["My name is <PERSON><PERSON><PERSON>. I'm an entrepreneur, software engineer and data scientist. I'm originally from London, England, but I moved to NYC in 2000.\n", "I love all foods, particularly French food, but strangely I'm repelled by almost all forms of cheese. I'm not allergic, I just hate the taste! I make an exception for cream cheese and mozarella though - cheesecake and pizza are the greatest.\n"]}], "source": ["def read_text_file(file_path):\n", "    \"\"\"Read text file and return its content\"\"\"\n", "    with open(file_path, 'r', encoding='utf-8') as file:\n", "        content = file.read()\n", "    return content\n", "\n", "# Example usage\n", "summary = read_text_file(\"summary.txt\")\n", "print(summary)"]}, {"cell_type": "code", "execution_count": 45, "id": "59238ef9", "metadata": {}, "outputs": [], "source": ["name='<PERSON><PERSON><PERSON>'"]}, {"cell_type": "code", "execution_count": 46, "id": "bd5ff69f", "metadata": {}, "outputs": [], "source": ["system_prompt = f\"You are acting as {name}. You are answering questions on {name}'s website, \\\n", "particularly questions related to {name}'s career, background, skills and experience. \\\n", "Your responsibility is to represent {name} for interactions on the website as faithfully as possible. \\\n", "You are given a summary of {name}'s background and LinkedIn profile which you can use to answer questions. \\\n", "Be professional and engaging, as if talking to a potential client or future employer who came across the website. \\\n", "If you don't know the answer, say so.\"\n", "\n", "system_prompt += f\"\\n\\n## Summary:\\n{summary}\\n\\n## LinkedIn Profile:\\n{linkedin}\\n\\n\"\n", "system_prompt += f\"With this context, please chat with the user, always staying in character as {name}.\"\n"]}, {"cell_type": "code", "execution_count": 47, "id": "df794d57", "metadata": {}, "outputs": [{"data": {"text/plain": ["\"You are acting as <PERSON><PERSON><PERSON>. You are answering questions on <PERSON><PERSON><PERSON>'s website, particularly questions related to <PERSON><PERSON><PERSON>'s career, background, skills and experience. Your responsibility is to represent <PERSON><PERSON><PERSON> for interactions on the website as faithfully as possible. You are given a summary of <PERSON><PERSON><PERSON>'s background and LinkedIn profile which you can use to answer questions. Be professional and engaging, as if talking to a potential client or future employer who came across the website. If you don't know the answer, say so.\\n\\n## Summary:\\nMy name is <PERSON><PERSON><PERSON>. I'm an entrepreneur, software engineer and data scientist. I'm originally from London, England, but I moved to NYC in 2000.\\nI love all foods, particularly French food, but strangely I'm repelled by almost all forms of cheese. I'm not allergic, I just hate the taste! I make an exception for cream cheese and mozarella though - cheesecake and pizza are the greatest.\\n\\n## LinkedIn Profile:\\n\\xa0 \\xa0\\nContact\\n5140 prancing pass cumming ga\\n**********  (Mobile)\\<EMAIL>\\nwww.linkedin.com/in/tushar-\\nmanna-3aa31810  (LinkedIn)\\nTop Skills\\nSoftware Development Life Cycle\\n(SDLC)\\nSQL\\nSoftware DevelopmentTushar Manna\\nSenior Software Engg at Lyric\\nAlpharetta, Georgia, United States\\nSummary\\nAccomplished IT professional with over 18 years+ experience in\\nHealthcare IT domain,,  Excel at leading cross functional teams such\\nas Requirements Team/Development and QA, providing strategic\\nand tactical direction and deciding upon applicable architectural\\nstrategies for successful deliverables.  Savvy with technology\\nincluding most ASP.Net, WCF, C #.NET , C++, VC++ (MFC), STL,\\nCOM/DCOM (ATL),Angular JS, TypeScript,Node JS Python ,Tibco\\nSpotfire , SQL Server,Oracle, Visual Studio 2017, Net MVC / Web\\nAPI, REST, OOP class design\\nExperience\\nLyric - Clarity in motion.\\nSr Software engineer\\nOctober 2023\\xa0-\\xa0Present\\xa0 (1 year 11 months)\\nSkills:Lifecycle Management · Communication · Scrum · Computer Science\\n· Secure File Transfer Protocol (SFTP) · Interpersonal Communication ·\\nInformation Technology · Performance Reviews · Data Privacy · Software\\nDevelopment Methodologies · Business Requirements · Coaching · Analytical\\nSkills · Leadership · TypeScript · Technical Engineering · Enterprise Software\\n· Software Architecture · Secure Shell (SSH) · HTML · Microsoft SQL Server ·\\nOracle- Problem Solving · Strategic Thinking\\nChange Healthcare\\nSenior Software Engineer\\nFebruary 2017\\xa0-\\xa0February 2024\\xa0 (7 years 1 month)\\nalpharetta \\nSkills:Lifecycle Management · Communication · Scrum · Computer Science\\n· Secure File Transfer Protocol (SFTP) · Interpersonal Communication ·\\nInformation Technology · Performance Reviews · Data Privacy · Software\\nDevelopment Methodologies · Business Requirements · Coaching · Analytical\\nSkills · Leadership · TypeScript · Technical Engineering · Enterprise Software\\n\\xa0 Page 1 of 3\\n\\xa0 \\xa0\\n· Software Architecture · Secure Shell (SSH) · HTML · Microsoft SQL Server ·\\nOracle- Problem Solving · Strategic Thinking\\nMcKesson\\nSenior Software Engg\\nDecember 2003\\xa0-\\xa0January 2017\\xa0 (13 years 2 months)\\nSkills:Lifecycle Management · Communication · Scrum · Computer Science\\n· Secure File Transfer Protocol (SFTP) · Interpersonal Communication ·\\nInformation Technology · Performance Reviews · Data Privacy · Software\\nDevelopment Methodologies · Business Requirements · Coaching · Analytical\\nSkills · Leadership · TypeScript · Technical Engineering · Enterprise Software\\n· Software Architecture · Secure Shell (SSH) · HTML · Microsoft SQL Server ·\\nOracle- Problem Solving · Strategic Thinking\\nJPMorgan Chase & Co.\\nSoftware engg\\nApril 2006\\xa0-\\xa0January 2007\\xa0 (10 months)\\nDover, Delaware Area\\nSkills: Management Professional · Skill Development · Identity & Access\\nManagement (IAM) · Application Lifecycle Management · Communication\\n·  Computer Science · Secure File Transfer Protocol (SFTP) · Interpersonal\\nCommunication · Information Technology · Performance Reviews · Data\\nPrivacy · Software Development Methodologies · Business Requirements ·\\nCoaching · Analytical Skills · Leadership · TypeScript · Technical Engineering\\n· Enterprise Software · Software Architecture · Secure Shell (SSH) · HTML ·\\nMicrosoft SQL Server ·Oracle-  Problem Solving · Strategic Thinking\\nSYNTELL Inc.\\nProject Lead\\nJuly 2003\\xa0-\\xa0March 2006\\xa0 (2 years 9 months)\\nSkills: Skill Development · Identity & Access Management (IAM) · Application\\nLifecycle Management · Communication ·  Computer Science · Secure\\nFile Transfer Protocol (SFTP) · Interpersonal Communication · Information\\nTechnology ·  Performance Reviews · Software Development Methodologies ·\\nBusiness Requirements · Coaching · Analytical Skills · Leadership · TypeScript\\n· Technical Engineering · Enterprise Software · Software Architecture · Secure\\nShell (SSH) · HTML · Microsoft SQL Server · Oracle- Problem Solving ·\\nStrategic Thinking\\nKPIT Cummins Infosystems Limited\\n\\xa0 Page 2 of 3\\n\\xa0 \\xa0\\nSystems Executive\\nDecember 2002\\xa0-\\xa0June 2003\\xa0 (7 months)\\nSkills: Skill Development · Application Lifecycle Management · Communication\\n· Computer Science · Secure File Transfer Protocol (SFTP) · Interpersonal\\nCommunication · Performance Reviews · Data Privacy · Software\\nDevelopment Methodologies · Business Requirements · Coaching · Analytical\\nSkills · Leadership · TypeScript · Technical Engineering · Enterprise Software\\n· Software Architecture · HTML · Microsoft SQL Server · Oracle - Problem\\nSolving\\nKnights Technology\\nSr Software Engg\\n2000\\xa0-\\xa02002\\xa0 (2 years)\\nSkills: Skill Development · Application Lifecycle Management · Communication\\n·  Computer Science · Interpersonal Communication · Performance Reviews\\n·  Software Development Methodologies · Business Requirements · Coaching\\n· Analytical Skills · Leadership · Technical Engineering · Enterprise Software ·\\nSoftware Architecture · HTML · Problem Solving\\nKnights\\nSoftware Engg\\n2000\\xa0-\\xa02002\\xa0 (2 years)\\nSkills: Six Sigma · Application Lifecycle Management · Computer Science\\n· Interpersonal Communication · · Business Requirements · Coaching ·\\nAnalytical Skills · Secure Shell (SSH) · Microsoft SQL Server · Problem Solving\\nEducation\\nUniversity of Mumbai\\nBE,\\xa0Production \\xa0·\\xa0(1995\\xa0-\\xa01999)\\nK.J.Somaiya College of Engg\\nBE,\\xa0Production \\xa0·\\xa0(1995\\xa0-\\xa01999)\\n\\xa0 Page 3 of 3\\n\\n\\nWith this context, please chat with the user, always staying in character as Tushar Manna.\""]}, "execution_count": 47, "metadata": {}, "output_type": "execute_result"}], "source": ["system_prompt"]}, {"cell_type": "code", "execution_count": 49, "id": "74be1ec1", "metadata": {}, "outputs": [], "source": ["def chat(message,history=[]):\n", "    messages = [{ \"role\": \"system\", \"content\": system_prompt}]+history+[{\"role\": \"user\", \"content\": message}]\n", "    response = client.chat.completions.create(model=\"gpt-4.1-nano\", messages=messages)\n", "    answer = response.choices[0].message.content\n", "    return answer"]}, {"cell_type": "code", "execution_count": 33, "id": "7edd0037", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Collecting gradio\n", "  Obtaining dependency information for gradio from https://files.pythonhosted.org/packages/d6/b6/be0898391fe9b34d591df564bb8f3790a69fd2e4d4f30c93c76abbcf48f2/gradio-5.42.0-py3-none-any.whl.metadata\n", "  Downloading gradio-5.42.0-py3-none-any.whl.metadata (16 kB)\n", "Requirement already satisfied: aiofiles<25.0,>=22.0 in c:\\users\\<USER>\\anaconda3\\lib\\site-packages (from gradio) (22.1.0)\n", "Requirement already satisfied: anyio<5.0,>=3.0 in c:\\users\\<USER>\\anaconda3\\lib\\site-packages (from gradio) (4.9.0)\n", "Collecting brotli>=1.1.0 (from gradio)\n", "  Obtaining dependency information for brotli>=1.1.0 from https://files.pythonhosted.org/packages/02/8a/fece0ee1057643cb2a5bbf59682de13f1725f8482b2c057d4e799d7ade75/Brotli-1.1.0-cp311-cp311-win_amd64.whl.metadata\n", "  Downloading Brotli-1.1.0-cp311-cp311-win_amd64.whl.metadata (5.6 kB)\n", "Requirement already satisfied: fastapi<1.0,>=0.115.2 in c:\\users\\<USER>\\anaconda3\\lib\\site-packages (from gradio) (0.115.9)\n", "Collecting ffmpy (from gradio)\n", "  Obtaining dependency information for ffmpy from https://files.pythonhosted.org/packages/74/d4/1806897b31c480efc4e97c22506ac46c716084f573aef780bb7fb7a16e8a/ffmpy-0.6.1-py3-none-any.whl.metadata\n", "  Downloading ffmpy-0.6.1-py3-none-any.whl.metadata (2.9 kB)\n", "Collecting gradio-client==1.11.1 (from gradio)\n", "  Obtaining dependency information for gradio-client==1.11.1 from https://files.pythonhosted.org/packages/c1/fe/b9d63453c4c3044ee96b4c7ac6b4331c543ca8d9195b2cd9ba299ecb6509/gradio_client-1.11.1-py3-none-any.whl.metadata\n", "  Downloading gradio_client-1.11.1-py3-none-any.whl.metadata (7.1 kB)\n", "Collecting groovy~=0.1 (from gradio)\n", "  Obtaining dependency information for groovy~=0.1 from https://files.pythonhosted.org/packages/28/27/3d6dcadc8a3214d8522c1e7f6a19554e33659be44546d44a2f7572ac7d2a/groovy-0.1.2-py3-none-any.whl.metadata\n", "  Downloading groovy-0.1.2-py3-none-any.whl.metadata (6.1 kB)\n", "Requirement already satisfied: httpx<1.0,>=0.24.1 in c:\\users\\<USER>\\anaconda3\\lib\\site-packages (from gradio) (0.28.1)\n", "Collecting huggingface-hub<1.0,>=0.33.5 (from gradio)\n", "  Obtaining dependency information for huggingface-hub<1.0,>=0.33.5 from https://files.pythonhosted.org/packages/39/7b/bb06b061991107cd8783f300adff3e7b7f284e330fd82f507f2a1417b11d/huggingface_hub-0.34.4-py3-none-any.whl.metadata\n", "  Downloading huggingface_hub-0.34.4-py3-none-any.whl.metadata (14 kB)\n", "Requirement already satisfied: jinja2<4.0 in c:\\users\\<USER>\\anaconda3\\lib\\site-packages (from gradio) (3.1.2)\n", "Requirement already satisfied: markupsafe<4.0,>=2.0 in c:\\users\\<USER>\\anaconda3\\lib\\site-packages (from gradio) (2.1.1)\n", "Requirement already satisfied: numpy<3.0,>=1.0 in c:\\users\\<USER>\\anaconda3\\lib\\site-packages (from gradio) (1.26.4)\n", "Requirement already satisfied: orjson~=3.0 in c:\\users\\<USER>\\anaconda3\\lib\\site-packages (from gradio) (3.10.16)\n", "Requirement already satisfied: packaging in c:\\users\\<USER>\\anaconda3\\lib\\site-packages (from gradio) (24.2)\n", "Requirement already satisfied: pandas<3.0,>=1.0 in c:\\users\\<USER>\\anaconda3\\lib\\site-packages (from gradio) (2.0.3)\n", "Requirement already satisfied: pillow<12.0,>=8.0 in c:\\users\\<USER>\\anaconda3\\lib\\site-packages (from gradio) (9.4.0)\n", "Requirement already satisfied: pydantic<2.12,>=2.0 in c:\\users\\<USER>\\anaconda3\\lib\\site-packages (from gradio) (2.11.3)\n", "Collecting pydub (from gradio)\n", "  Obtaining dependency information for pydub from https://files.pythonhosted.org/packages/a6/53/d78dc063216e62fc55f6b2eebb447f6a4b0a59f55c8406376f76bf959b08/pydub-0.25.1-py2.py3-none-any.whl.metadata\n", "  Downloading pydub-0.25.1-py2.py3-none-any.whl.metadata (1.4 kB)\n", "Collecting python-multipart>=0.0.18 (from gradio)\n", "  Obtaining dependency information for python-multipart>=0.0.18 from https://files.pythonhosted.org/packages/45/58/38b5afbc1a800eeea951b9285d3912613f2603bdf897a4ab0f4bd7f405fc/python_multipart-0.0.20-py3-none-any.whl.metadata\n", "  Downloading python_multipart-0.0.20-py3-none-any.whl.metadata (1.8 kB)\n", "Requirement already satisfied: pyyaml<7.0,>=5.0 in c:\\users\\<USER>\\anaconda3\\lib\\site-packages (from gradio) (6.0)\n", "Collecting ruff>=0.9.3 (from gradio)\n", "  Obtaining dependency information for ruff>=0.9.3 from https://files.pythonhosted.org/packages/f6/dc/a2873b7c5001c62f46266685863bee2888caf469d1edac84bf3242074be2/ruff-0.12.8-py3-none-win_amd64.whl.metadata\n", "  Downloading ruff-0.12.8-py3-none-win_amd64.whl.metadata (26 kB)\n", "Collecting safehttpx<0.2.0,>=0.1.6 (from gradio)\n", "  Obtaining dependency information for safehttpx<0.2.0,>=0.1.6 from https://files.pythonhosted.org/packages/4d/c0/1108ad9f01567f66b3154063605b350b69c3c9366732e09e45f9fd0d1deb/safehttpx-0.1.6-py3-none-any.whl.metadata\n", "  Downloading safehttpx-0.1.6-py3-none-any.whl.metadata (4.2 kB)\n", "Collecting semantic-version~=2.0 (from gradio)\n", "  Obtaining dependency information for semantic-version~=2.0 from https://files.pythonhosted.org/packages/6a/23/8146aad7d88f4fcb3a6218f41a60f6c2d4e3a72de72da1825dc7c8f7877c/semantic_version-2.10.0-py2.py3-none-any.whl.metadata\n", "  Downloading semantic_version-2.10.0-py2.py3-none-any.whl.metadata (9.7 kB)\n", "Requirement already satisfied: starlette<1.0,>=0.40.0 in c:\\users\\<USER>\\anaconda3\\lib\\site-packages (from gradio) (0.45.3)\n", "Collecting tomlkit<0.14.0,>=0.12.0 (from gradio)\n", "  Obtaining dependency information for tomlkit<0.14.0,>=0.12.0 from https://files.pythonhosted.org/packages/bd/75/8539d011f6be8e29f339c42e633aae3cb73bffa95dd0f9adec09b9c58e85/tomlkit-0.13.3-py3-none-any.whl.metadata\n", "  Downloading tomlkit-0.13.3-py3-none-any.whl.metadata (2.8 kB)\n", "Requirement already satisfied: typer<1.0,>=0.12 in c:\\users\\<USER>\\anaconda3\\lib\\site-packages (from gradio) (0.15.2)\n", "Requirement already satisfied: typing-extensions~=4.0 in c:\\users\\<USER>\\anaconda3\\lib\\site-packages (from gradio) (4.12.2)\n", "Requirement already satisfied: uvicorn>=0.14.0 in c:\\users\\<USER>\\anaconda3\\lib\\site-packages (from gradio) (0.34.1)\n", "Requirement already satisfied: fsspec in c:\\users\\<USER>\\anaconda3\\lib\\site-packages (from gradio-client==1.11.1->gradio) (2024.12.0)\n", "Requirement already satisfied: websockets<16.0,>=10.0 in c:\\users\\<USER>\\anaconda3\\lib\\site-packages (from gradio-client==1.11.1->gradio) (15.0.1)\n", "Requirement already satisfied: idna>=2.8 in c:\\users\\<USER>\\anaconda3\\lib\\site-packages (from anyio<5.0,>=3.0->gradio) (3.4)\n", "Requirement already satisfied: sniffio>=1.1 in c:\\users\\<USER>\\anaconda3\\lib\\site-packages (from anyio<5.0,>=3.0->gradio) (1.2.0)\n", "Requirement already satisfied: certifi in c:\\users\\<USER>\\anaconda3\\lib\\site-packages (from httpx<1.0,>=0.24.1->gradio) (2023.7.22)\n", "Requirement already satisfied: httpcore==1.* in c:\\users\\<USER>\\anaconda3\\lib\\site-packages (from httpx<1.0,>=0.24.1->gradio) (1.0.8)\n", "Requirement already satisfied: h11<0.15,>=0.13 in c:\\users\\<USER>\\anaconda3\\lib\\site-packages (from httpcore==1.*->httpx<1.0,>=0.24.1->gradio) (0.14.0)\n", "Requirement already satisfied: filelock in c:\\users\\<USER>\\anaconda3\\lib\\site-packages (from huggingface-hub<1.0,>=0.33.5->gradio) (3.9.0)\n", "Requirement already satisfied: requests in c:\\users\\<USER>\\anaconda3\\lib\\site-packages (from huggingface-hub<1.0,>=0.33.5->gradio) (2.32.3)\n", "Requirement already satisfied: tqdm>=4.42.1 in c:\\users\\<USER>\\anaconda3\\lib\\site-packages (from huggingface-hub<1.0,>=0.33.5->gradio) (4.67.1)\n", "Requirement already satisfied: python-dateutil>=2.8.2 in c:\\users\\<USER>\\anaconda3\\lib\\site-packages (from pandas<3.0,>=1.0->gradio) (2.8.2)\n", "Requirement already satisfied: pytz>=2020.1 in c:\\users\\<USER>\\anaconda3\\lib\\site-packages (from pandas<3.0,>=1.0->gradio) (2023.3.post1)\n", "Requirement already satisfied: tzdata>=2022.1 in c:\\users\\<USER>\\anaconda3\\lib\\site-packages (from pandas<3.0,>=1.0->gradio) (2023.3)\n", "Requirement already satisfied: annotated-types>=0.6.0 in c:\\users\\<USER>\\anaconda3\\lib\\site-packages (from pydantic<2.12,>=2.0->gradio) (0.7.0)\n", "Requirement already satisfied: pydantic-core==2.33.1 in c:\\users\\<USER>\\anaconda3\\lib\\site-packages (from pydantic<2.12,>=2.0->gradio) (2.33.1)\n", "Requirement already satisfied: typing-inspection>=0.4.0 in c:\\users\\<USER>\\anaconda3\\lib\\site-packages (from pydantic<2.12,>=2.0->gradio) (0.4.0)\n", "Requirement already satisfied: click>=8.0.0 in c:\\users\\<USER>\\anaconda3\\lib\\site-packages (from typer<1.0,>=0.12->gradio) (8.0.4)\n", "Requirement already satisfied: shellingham>=1.3.0 in c:\\users\\<USER>\\anaconda3\\lib\\site-packages (from typer<1.0,>=0.12->gradio) (1.5.4)\n", "Requirement already satisfied: rich>=10.11.0 in c:\\users\\<USER>\\anaconda3\\lib\\site-packages (from typer<1.0,>=0.12->gradio) (14.0.0)\n", "Requirement already satisfied: colorama in c:\\users\\<USER>\\anaconda3\\lib\\site-packages (from click>=8.0.0->typer<1.0,>=0.12->gradio) (0.4.6)\n", "Requirement already satisfied: six>=1.5 in c:\\users\\<USER>\\anaconda3\\lib\\site-packages (from python-dateutil>=2.8.2->pandas<3.0,>=1.0->gradio) (1.16.0)\n", "Requirement already satisfied: markdown-it-py>=2.2.0 in c:\\users\\<USER>\\anaconda3\\lib\\site-packages (from rich>=10.11.0->typer<1.0,>=0.12->gradio) (2.2.0)\n", "Requirement already satisfied: pygments<3.0.0,>=2.13.0 in c:\\users\\<USER>\\anaconda3\\lib\\site-packages (from rich>=10.11.0->typer<1.0,>=0.12->gradio) (2.15.1)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in c:\\users\\<USER>\\anaconda3\\lib\\site-packages (from requests->huggingface-hub<1.0,>=0.33.5->gradio) (2.0.4)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in c:\\users\\<USER>\\anaconda3\\lib\\site-packages (from requests->huggingface-hub<1.0,>=0.33.5->gradio) (1.26.16)\n", "Requirement already satisfied: mdurl~=0.1 in c:\\users\\<USER>\\anaconda3\\lib\\site-packages (from markdown-it-py>=2.2.0->rich>=10.11.0->typer<1.0,>=0.12->gradio) (0.1.0)\n", "Downloading gradio-5.42.0-py3-none-any.whl (59.7 MB)\n", "   ---------------------------------------- 0.0/59.7 MB ? eta -:--:--\n", "   ---------------------------------------- 0.1/59.7 MB 1.7 MB/s eta 0:00:37\n", "   ---------------------------------------- 0.2/59.7 MB 1.5 MB/s eta 0:00:40\n", "   ---------------------------------------- 0.3/59.7 MB 1.8 MB/s eta 0:00:34\n", "   ---------------------------------------- 0.4/59.7 MB 1.9 MB/s eta 0:00:31\n", "   ---------------------------------------- 0.5/59.7 MB 2.1 MB/s eta 0:00:29\n", "   ---------------------------------------- 0.6/59.7 MB 2.3 MB/s eta 0:00:27\n", "    --------------------------------------- 0.8/59.7 MB 2.5 MB/s eta 0:00:24\n", "    --------------------------------------- 1.0/59.7 MB 2.6 MB/s eta 0:00:23\n", "    --------------------------------------- 1.2/59.7 MB 2.8 MB/s eta 0:00:21\n", "    --------------------------------------- 1.4/59.7 MB 3.0 MB/s eta 0:00:20\n", "   - -------------------------------------- 1.6/59.7 MB 3.0 MB/s eta 0:00:20\n", "   - -------------------------------------- 1.7/59.7 MB 3.2 MB/s eta 0:00:19\n", "   - -------------------------------------- 2.0/59.7 MB 3.2 MB/s eta 0:00:18\n", "   - -------------------------------------- 2.2/59.7 MB 3.3 MB/s eta 0:00:18\n", "   - -------------------------------------- 2.4/59.7 MB 3.4 MB/s eta 0:00:17\n", "   - -------------------------------------- 2.6/59.7 MB 3.5 MB/s eta 0:00:17\n", "   - -------------------------------------- 2.9/59.7 MB 3.7 MB/s eta 0:00:16\n", "   -- ------------------------------------- 3.1/59.7 MB 3.7 MB/s eta 0:00:16\n", "   -- ------------------------------------- 3.4/59.7 MB 3.8 MB/s eta 0:00:15\n", "   -- ------------------------------------- 3.6/59.7 MB 3.8 MB/s eta 0:00:15\n", "   -- ------------------------------------- 3.8/59.7 MB 3.9 MB/s eta 0:00:15\n", "   -- ------------------------------------- 4.1/59.7 MB 4.0 MB/s eta 0:00:15\n", "   -- ------------------------------------- 4.4/59.7 MB 4.0 MB/s eta 0:00:14\n", "   --- ------------------------------------ 4.7/59.7 MB 4.2 MB/s eta 0:00:14\n", "   --- ------------------------------------ 5.0/59.7 MB 4.3 MB/s eta 0:00:13\n", "   --- ------------------------------------ 5.2/59.7 MB 4.3 MB/s eta 0:00:13\n", "   --- ------------------------------------ 5.6/59.7 MB 4.4 MB/s eta 0:00:13\n", "   --- ------------------------------------ 5.9/59.7 MB 4.5 MB/s eta 0:00:13\n", "   ---- ----------------------------------- 6.3/59.7 MB 4.6 MB/s eta 0:00:12\n", "   ---- ----------------------------------- 6.7/59.7 MB 4.7 MB/s eta 0:00:12\n", "   ---- ----------------------------------- 7.0/59.7 MB 4.8 MB/s eta 0:00:12\n", "   ---- ----------------------------------- 7.2/59.7 MB 4.8 MB/s eta 0:00:11\n", "   ----- ---------------------------------- 7.6/59.7 MB 4.9 MB/s eta 0:00:11\n", "   ----- ---------------------------------- 7.9/59.7 MB 4.9 MB/s eta 0:00:11\n", "   ----- ---------------------------------- 8.3/59.7 MB 5.0 MB/s eta 0:00:11\n", "   ----- ---------------------------------- 8.7/59.7 MB 5.1 MB/s eta 0:00:11\n", "   ----- ---------------------------------- 8.9/59.7 MB 5.1 MB/s eta 0:00:10\n", "   ------ --------------------------------- 9.3/59.7 MB 5.2 MB/s eta 0:00:10\n", "   ------ --------------------------------- 9.7/59.7 MB 5.2 MB/s eta 0:00:10\n", "   ------ --------------------------------- 9.9/59.7 MB 5.2 MB/s eta 0:00:10\n", "   ------ --------------------------------- 10.2/59.7 MB 5.3 MB/s eta 0:00:10\n", "   ------- -------------------------------- 10.6/59.7 MB 5.6 MB/s eta 0:00:09\n", "   ------- -------------------------------- 10.9/59.7 MB 5.8 MB/s eta 0:00:09\n", "   ------- -------------------------------- 11.4/59.7 MB 6.1 MB/s eta 0:00:08\n", "   ------- -------------------------------- 11.7/59.7 MB 6.2 MB/s eta 0:00:08\n", "   -------- ------------------------------- 12.1/59.7 MB 6.4 MB/s eta 0:00:08\n", "   -------- ------------------------------- 12.3/59.7 MB 6.4 MB/s eta 0:00:08\n", "   -------- ------------------------------- 12.7/59.7 MB 6.5 MB/s eta 0:00:08\n", "   -------- ------------------------------- 12.8/59.7 MB 6.5 MB/s eta 0:00:08\n", "   -------- ------------------------------- 13.0/59.7 MB 6.5 MB/s eta 0:00:08\n", "   -------- ------------------------------- 13.4/59.7 MB 6.5 MB/s eta 0:00:08\n", "   --------- ------------------------------ 13.8/59.7 MB 6.6 MB/s eta 0:00:07\n", "   --------- ------------------------------ 13.8/59.7 MB 6.6 MB/s eta 0:00:07\n", "   --------- ------------------------------ 14.4/59.7 MB 6.6 MB/s eta 0:00:07\n", "   --------- ------------------------------ 14.4/59.7 MB 6.6 MB/s eta 0:00:07\n", "   --------- ------------------------------ 14.7/59.7 MB 6.5 MB/s eta 0:00:07\n", "   ---------- ----------------------------- 15.1/59.7 MB 6.5 MB/s eta 0:00:07\n", "   ---------- ----------------------------- 15.5/59.7 MB 6.5 MB/s eta 0:00:07\n", "   ---------- ----------------------------- 15.6/59.7 MB 6.5 MB/s eta 0:00:07\n", "   ---------- ----------------------------- 16.0/59.7 MB 6.5 MB/s eta 0:00:07\n", "   ---------- ----------------------------- 16.3/59.7 MB 6.4 MB/s eta 0:00:07\n", "   ----------- ---------------------------- 16.5/59.7 MB 6.4 MB/s eta 0:00:07\n", "   ----------- ---------------------------- 16.9/59.7 MB 6.4 MB/s eta 0:00:07\n", "   ----------- ---------------------------- 17.2/59.7 MB 6.3 MB/s eta 0:00:07\n", "   ----------- ---------------------------- 17.6/59.7 MB 6.3 MB/s eta 0:00:07\n", "   ----------- ---------------------------- 17.9/59.7 MB 6.3 MB/s eta 0:00:07\n", "   ------------ --------------------------- 18.2/59.7 MB 6.3 MB/s eta 0:00:07\n", "   ------------ --------------------------- 18.5/59.7 MB 6.2 MB/s eta 0:00:07\n", "   ------------ --------------------------- 18.8/59.7 MB 6.2 MB/s eta 0:00:07\n", "   ------------ --------------------------- 19.1/59.7 MB 6.2 MB/s eta 0:00:07\n", "   ------------ --------------------------- 19.4/59.7 MB 6.2 MB/s eta 0:00:07\n", "   ------------- -------------------------- 19.7/59.7 MB 6.2 MB/s eta 0:00:07\n", "   ------------- -------------------------- 19.9/59.7 MB 6.1 MB/s eta 0:00:07\n", "   ------------- -------------------------- 20.3/59.7 MB 6.1 MB/s eta 0:00:07\n", "   ------------- -------------------------- 20.6/59.7 MB 6.1 MB/s eta 0:00:07\n", "   ------------- -------------------------- 20.7/59.7 MB 6.1 MB/s eta 0:00:07\n", "   -------------- ------------------------- 21.2/59.7 MB 6.0 MB/s eta 0:00:07\n", "   -------------- ------------------------- 21.4/59.7 MB 6.0 MB/s eta 0:00:07\n", "   -------------- ------------------------- 21.6/59.7 MB 5.8 MB/s eta 0:00:07\n", "   -------------- ------------------------- 22.1/59.7 MB 5.8 MB/s eta 0:00:07\n", "   -------------- ------------------------- 22.1/59.7 MB 5.7 MB/s eta 0:00:07\n", "   -------------- ------------------------- 22.4/59.7 MB 5.7 MB/s eta 0:00:07\n", "   --------------- ------------------------ 22.7/59.7 MB 5.7 MB/s eta 0:00:07\n", "   --------------- ------------------------ 22.9/59.7 MB 5.7 MB/s eta 0:00:07\n", "   --------------- ------------------------ 23.2/59.7 MB 5.7 MB/s eta 0:00:07\n", "   --------------- ------------------------ 23.4/59.7 MB 5.6 MB/s eta 0:00:07\n", "   --------------- ------------------------ 23.6/59.7 MB 5.6 MB/s eta 0:00:07\n", "   ---------------- ----------------------- 23.9/59.7 MB 5.5 MB/s eta 0:00:07\n", "   ---------------- ----------------------- 24.1/59.7 MB 5.6 MB/s eta 0:00:07\n", "   ---------------- ----------------------- 24.3/59.7 MB 5.5 MB/s eta 0:00:07\n", "   ---------------- ----------------------- 24.6/59.7 MB 5.5 MB/s eta 0:00:07\n", "   ---------------- ----------------------- 24.9/59.7 MB 5.5 MB/s eta 0:00:07\n", "   ---------------- ----------------------- 25.1/59.7 MB 5.5 MB/s eta 0:00:07\n", "   ----------------- ---------------------- 25.4/59.7 MB 5.5 MB/s eta 0:00:07\n", "   ----------------- ---------------------- 25.7/59.7 MB 5.5 MB/s eta 0:00:07\n", "   ----------------- ---------------------- 25.9/59.7 MB 5.5 MB/s eta 0:00:07\n", "   ----------------- ---------------------- 26.1/59.7 MB 5.4 MB/s eta 0:00:07\n", "   ----------------- ---------------------- 26.4/59.7 MB 5.4 MB/s eta 0:00:07\n", "   ----------------- ---------------------- 26.5/59.7 MB 5.4 MB/s eta 0:00:07\n", "   ----------------- ---------------------- 26.8/59.7 MB 5.4 MB/s eta 0:00:07\n", "   ------------------ --------------------- 26.9/59.7 MB 5.3 MB/s eta 0:00:07\n", "   ------------------ --------------------- 27.2/59.7 MB 5.3 MB/s eta 0:00:07\n", "   ------------------ --------------------- 27.4/59.7 MB 5.2 MB/s eta 0:00:07\n", "   ------------------ --------------------- 27.6/59.7 MB 5.1 MB/s eta 0:00:07\n", "   ------------------ --------------------- 27.9/59.7 MB 5.1 MB/s eta 0:00:07\n", "   ------------------ --------------------- 28.1/59.7 MB 5.0 MB/s eta 0:00:07\n", "   ------------------ --------------------- 28.3/59.7 MB 5.0 MB/s eta 0:00:07\n", "   ------------------- -------------------- 28.5/59.7 MB 5.0 MB/s eta 0:00:07\n", "   ------------------- -------------------- 28.9/59.7 MB 5.0 MB/s eta 0:00:07\n", "   ------------------- -------------------- 29.2/59.7 MB 4.9 MB/s eta 0:00:07\n", "   ------------------- -------------------- 29.3/59.7 MB 4.9 MB/s eta 0:00:07\n", "   ------------------- -------------------- 29.6/59.7 MB 4.9 MB/s eta 0:00:07\n", "   -------------------- ------------------- 30.0/59.7 MB 4.9 MB/s eta 0:00:07\n", "   -------------------- ------------------- 30.2/59.7 MB 4.9 MB/s eta 0:00:06\n", "   -------------------- ------------------- 30.5/59.7 MB 4.9 MB/s eta 0:00:06\n", "   -------------------- ------------------- 30.9/59.7 MB 4.9 MB/s eta 0:00:06\n", "   -------------------- ------------------- 31.1/59.7 MB 4.9 MB/s eta 0:00:06\n", "   --------------------- ------------------ 31.4/59.7 MB 4.9 MB/s eta 0:00:06\n", "   --------------------- ------------------ 31.7/59.7 MB 5.0 MB/s eta 0:00:06\n", "   --------------------- ------------------ 32.0/59.7 MB 5.0 MB/s eta 0:00:06\n", "   --------------------- ------------------ 32.3/59.7 MB 4.9 MB/s eta 0:00:06\n", "   --------------------- ------------------ 32.6/59.7 MB 5.1 MB/s eta 0:00:06\n", "   ---------------------- ----------------- 32.9/59.7 MB 5.0 MB/s eta 0:00:06\n", "   ---------------------- ----------------- 33.2/59.7 MB 5.0 MB/s eta 0:00:06\n", "   ---------------------- ----------------- 33.4/59.7 MB 5.1 MB/s eta 0:00:06\n", "   ---------------------- ----------------- 33.6/59.7 MB 5.1 MB/s eta 0:00:06\n", "   ---------------------- ----------------- 33.9/59.7 MB 5.1 MB/s eta 0:00:06\n", "   ---------------------- ----------------- 34.2/59.7 MB 5.1 MB/s eta 0:00:05\n", "   ----------------------- ---------------- 34.4/59.7 MB 5.2 MB/s eta 0:00:05\n", "   ----------------------- ---------------- 34.5/59.7 MB 5.0 MB/s eta 0:00:05\n", "   ----------------------- ---------------- 34.8/59.7 MB 5.1 MB/s eta 0:00:05\n", "   ----------------------- ---------------- 35.1/59.7 MB 5.1 MB/s eta 0:00:05\n", "   ----------------------- ---------------- 35.3/59.7 MB 5.1 MB/s eta 0:00:05\n", "   ----------------------- ---------------- 35.5/59.7 MB 5.0 MB/s eta 0:00:05\n", "   ----------------------- ---------------- 35.7/59.7 MB 5.0 MB/s eta 0:00:05\n", "   ------------------------ --------------- 36.1/59.7 MB 5.1 MB/s eta 0:00:05\n", "   ------------------------ --------------- 36.3/59.7 MB 5.0 MB/s eta 0:00:05\n", "   ------------------------ --------------- 36.5/59.7 MB 5.0 MB/s eta 0:00:05\n", "   ------------------------ --------------- 36.8/59.7 MB 5.2 MB/s eta 0:00:05\n", "   ------------------------ --------------- 37.1/59.7 MB 5.2 MB/s eta 0:00:05\n", "   ------------------------- -------------- 37.4/59.7 MB 5.2 MB/s eta 0:00:05\n", "   ------------------------- -------------- 37.6/59.7 MB 5.2 MB/s eta 0:00:05\n", "   ------------------------- -------------- 37.8/59.7 MB 5.2 MB/s eta 0:00:05\n", "   ------------------------- -------------- 38.2/59.7 MB 5.2 MB/s eta 0:00:05\n", "   ------------------------- -------------- 38.3/59.7 MB 5.2 MB/s eta 0:00:05\n", "   ------------------------- -------------- 38.6/59.7 MB 5.3 MB/s eta 0:00:04\n", "   -------------------------- ------------- 39.0/59.7 MB 5.3 MB/s eta 0:00:04\n", "   -------------------------- ------------- 39.3/59.7 MB 5.4 MB/s eta 0:00:04\n", "   -------------------------- ------------- 39.7/59.7 MB 5.5 MB/s eta 0:00:04\n", "   -------------------------- ------------- 39.9/59.7 MB 5.4 MB/s eta 0:00:04\n", "   -------------------------- ------------- 40.2/59.7 MB 5.5 MB/s eta 0:00:04\n", "   --------------------------- ------------ 40.5/59.7 MB 5.5 MB/s eta 0:00:04\n", "   --------------------------- ------------ 40.8/59.7 MB 5.5 MB/s eta 0:00:04\n", "   --------------------------- ------------ 41.2/59.7 MB 5.5 MB/s eta 0:00:04\n", "   --------------------------- ------------ 41.5/59.7 MB 5.5 MB/s eta 0:00:04\n", "   --------------------------- ------------ 41.8/59.7 MB 5.5 MB/s eta 0:00:04\n", "   ---------------------------- ----------- 42.1/59.7 MB 5.5 MB/s eta 0:00:04\n", "   ---------------------------- ----------- 42.4/59.7 MB 5.5 MB/s eta 0:00:04\n", "   ---------------------------- ----------- 42.9/59.7 MB 5.5 MB/s eta 0:00:04\n", "   ---------------------------- ----------- 42.9/59.7 MB 5.5 MB/s eta 0:00:04\n", "   ----------------------------- ---------- 43.3/59.7 MB 5.5 MB/s eta 0:00:04\n", "   ----------------------------- ---------- 43.6/59.7 MB 5.6 MB/s eta 0:00:03\n", "   ----------------------------- ---------- 44.0/59.7 MB 5.6 MB/s eta 0:00:03\n", "   ----------------------------- ---------- 44.1/59.7 MB 5.6 MB/s eta 0:00:03\n", "   ----------------------------- ---------- 44.6/59.7 MB 5.7 MB/s eta 0:00:03\n", "   ------------------------------ --------- 44.9/59.7 MB 5.7 MB/s eta 0:00:03\n", "   ------------------------------ --------- 45.2/59.7 MB 5.8 MB/s eta 0:00:03\n", "   ------------------------------ --------- 45.5/59.7 MB 5.9 MB/s eta 0:00:03\n", "   ------------------------------ --------- 45.8/59.7 MB 6.0 MB/s eta 0:00:03\n", "   ------------------------------ --------- 45.9/59.7 MB 5.9 MB/s eta 0:00:03\n", "   ------------------------------- -------- 46.4/59.7 MB 6.1 MB/s eta 0:00:03\n", "   ------------------------------- -------- 46.7/59.7 MB 6.0 MB/s eta 0:00:03\n", "   ------------------------------- -------- 47.0/59.7 MB 6.1 MB/s eta 0:00:03\n", "   ------------------------------- -------- 47.5/59.7 MB 6.1 MB/s eta 0:00:02\n", "   ------------------------------- -------- 47.7/59.7 MB 6.2 MB/s eta 0:00:02\n", "   -------------------------------- ------- 48.1/59.7 MB 6.3 MB/s eta 0:00:02\n", "   -------------------------------- ------- 48.2/59.7 MB 6.3 MB/s eta 0:00:02\n", "   -------------------------------- ------- 48.8/59.7 MB 6.4 MB/s eta 0:00:02\n", "   -------------------------------- ------- 49.1/59.7 MB 6.3 MB/s eta 0:00:02\n", "   --------------------------------- ------ 49.3/59.7 MB 6.3 MB/s eta 0:00:02\n", "   --------------------------------- ------ 49.7/59.7 MB 6.3 MB/s eta 0:00:02\n", "   --------------------------------- ------ 50.0/59.7 MB 6.3 MB/s eta 0:00:02\n", "   --------------------------------- ------ 50.3/59.7 MB 6.4 MB/s eta 0:00:02\n", "   --------------------------------- ------ 50.6/59.7 MB 6.4 MB/s eta 0:00:02\n", "   ---------------------------------- ----- 50.9/59.7 MB 6.4 MB/s eta 0:00:02\n", "   ---------------------------------- ----- 51.3/59.7 MB 6.4 MB/s eta 0:00:02\n", "   ---------------------------------- ----- 51.6/59.7 MB 6.3 MB/s eta 0:00:02\n", "   ---------------------------------- ----- 51.8/59.7 MB 6.3 MB/s eta 0:00:02\n", "   ---------------------------------- ----- 52.1/59.7 MB 6.4 MB/s eta 0:00:02\n", "   ----------------------------------- ---- 52.5/59.7 MB 6.2 MB/s eta 0:00:02\n", "   ----------------------------------- ---- 52.8/59.7 MB 6.3 MB/s eta 0:00:02\n", "   ----------------------------------- ---- 53.0/59.7 MB 6.3 MB/s eta 0:00:02\n", "   ----------------------------------- ---- 53.3/59.7 MB 6.3 MB/s eta 0:00:02\n", "   ----------------------------------- ---- 53.7/59.7 MB 6.3 MB/s eta 0:00:01\n", "   ------------------------------------ --- 54.0/59.7 MB 6.3 MB/s eta 0:00:01\n", "   ------------------------------------ --- 54.3/59.7 MB 6.3 MB/s eta 0:00:01\n", "   ------------------------------------ --- 54.6/59.7 MB 6.3 MB/s eta 0:00:01\n", "   ------------------------------------ --- 54.9/59.7 MB 6.3 MB/s eta 0:00:01\n", "   ------------------------------------- -- 55.2/59.7 MB 6.4 MB/s eta 0:00:01\n", "   ------------------------------------- -- 55.5/59.7 MB 6.2 MB/s eta 0:00:01\n", "   ------------------------------------- -- 55.9/59.7 MB 6.4 MB/s eta 0:00:01\n", "   ------------------------------------- -- 56.1/59.7 MB 6.3 MB/s eta 0:00:01\n", "   ------------------------------------- -- 56.5/59.7 MB 6.4 MB/s eta 0:00:01\n", "   ------------------------------------- -- 56.6/59.7 MB 6.3 MB/s eta 0:00:01\n", "   -------------------------------------- - 56.7/59.7 MB 6.2 MB/s eta 0:00:01\n", "   -------------------------------------- - 56.8/59.7 MB 6.0 MB/s eta 0:00:01\n", "   -------------------------------------- - 56.8/59.7 MB 5.8 MB/s eta 0:00:01\n", "   -------------------------------------- - 56.8/59.7 MB 5.8 MB/s eta 0:00:01\n", "   -------------------------------------- - 56.9/59.7 MB 5.7 MB/s eta 0:00:01\n", "   -------------------------------------- - 57.1/59.7 MB 5.5 MB/s eta 0:00:01\n", "   -------------------------------------- - 57.3/59.7 MB 5.5 MB/s eta 0:00:01\n", "   -------------------------------------- - 57.4/59.7 MB 5.4 MB/s eta 0:00:01\n", "   -------------------------------------- - 57.7/59.7 MB 5.4 MB/s eta 0:00:01\n", "   -------------------------------------- - 57.8/59.7 MB 5.4 MB/s eta 0:00:01\n", "   -------------------------------------- - 58.1/59.7 MB 5.3 MB/s eta 0:00:01\n", "   ---------------------------------------  58.4/59.7 MB 5.3 MB/s eta 0:00:01\n", "   ---------------------------------------  58.6/59.7 MB 5.3 MB/s eta 0:00:01\n", "   ---------------------------------------  58.9/59.7 MB 5.3 MB/s eta 0:00:01\n", "   ---------------------------------------  59.1/59.7 MB 5.2 MB/s eta 0:00:01\n", "   ---------------------------------------  59.3/59.7 MB 5.2 MB/s eta 0:00:01\n", "   ---------------------------------------  59.6/59.7 MB 5.3 MB/s eta 0:00:01\n", "   ---------------------------------------  59.7/59.7 MB 5.2 MB/s eta 0:00:01\n", "   ---------------------------------------  59.7/59.7 MB 5.2 MB/s eta 0:00:01\n", "   ---------------------------------------  59.7/59.7 MB 5.2 MB/s eta 0:00:01\n", "   ---------------------------------------  59.7/59.7 MB 5.2 MB/s eta 0:00:01\n", "   ---------------------------------------- 59.7/59.7 MB 4.7 MB/s eta 0:00:00\n", "Downloading gradio_client-1.11.1-py3-none-any.whl (324 kB)\n", "   ---------------------------------------- 0.0/324.5 kB ? eta -:--:--\n", "   ------------------------------- -------- 256.0/324.5 kB 7.9 MB/s eta 0:00:01\n", "   ---------------------------------------- 324.5/324.5 kB 5.1 MB/s eta 0:00:00\n", "Downloading Brotli-1.1.0-cp311-cp311-win_amd64.whl (357 kB)\n", "   ---------------------------------------- 0.0/357.3 kB ? eta -:--:--\n", "   ---------------------------- ----------- 256.0/357.3 kB 5.2 MB/s eta 0:00:01\n", "   ---------------------------------------- 357.3/357.3 kB 4.4 MB/s eta 0:00:00\n", "Downloading groovy-0.1.2-py3-none-any.whl (14 kB)\n", "Downloading huggingface_hub-0.34.4-py3-none-any.whl (561 kB)\n", "   ---------------------------------------- 0.0/561.5 kB ? eta -:--:--\n", "   ---------------- ----------------------- 225.3/561.5 kB 6.7 MB/s eta 0:00:01\n", "   ----------------------------------- ---- 501.8/561.5 kB 6.3 MB/s eta 0:00:01\n", "   ---------------------------------------- 561.5/561.5 kB 4.4 MB/s eta 0:00:00\n", "Downloading python_multipart-0.0.20-py3-none-any.whl (24 kB)\n", "Downloading ruff-0.12.8-py3-none-win_amd64.whl (12.7 MB)\n", "   ---------------------------------------- 0.0/12.7 MB ? eta -:--:--\n", "    --------------------------------------- 0.3/12.7 MB 9.6 MB/s eta 0:00:02\n", "   - -------------------------------------- 0.6/12.7 MB 7.3 MB/s eta 0:00:02\n", "   --- ------------------------------------ 1.0/12.7 MB 7.2 MB/s eta 0:00:02\n", "   ---- ----------------------------------- 1.4/12.7 MB 8.0 MB/s eta 0:00:02\n", "   ----- ---------------------------------- 1.6/12.7 MB 7.4 MB/s eta 0:00:02\n", "   ------ --------------------------------- 2.0/12.7 MB 7.4 MB/s eta 0:00:02\n", "   ------- -------------------------------- 2.3/12.7 MB 7.4 MB/s eta 0:00:02\n", "   -------- ------------------------------- 2.6/12.7 MB 7.3 MB/s eta 0:00:02\n", "   --------- ------------------------------ 3.0/12.7 MB 7.0 MB/s eta 0:00:02\n", "   ---------- ----------------------------- 3.3/12.7 MB 7.2 MB/s eta 0:00:02\n", "   ----------- ---------------------------- 3.6/12.7 MB 6.9 MB/s eta 0:00:02\n", "   ----------- ---------------------------- 3.8/12.7 MB 6.7 MB/s eta 0:00:02\n", "   ------------- -------------------------- 4.2/12.7 MB 6.6 MB/s eta 0:00:02\n", "   ------------- -------------------------- 4.2/12.7 MB 6.6 MB/s eta 0:00:02\n", "   -------------- ------------------------- 4.6/12.7 MB 6.4 MB/s eta 0:00:02\n", "   --------------- ------------------------ 4.9/12.7 MB 6.4 MB/s eta 0:00:02\n", "   ---------------- ----------------------- 5.1/12.7 MB 6.3 MB/s eta 0:00:02\n", "   ---------------- ----------------------- 5.4/12.7 MB 6.3 MB/s eta 0:00:02\n", "   ------------------ --------------------- 5.8/12.7 MB 6.2 MB/s eta 0:00:02\n", "   ------------------ --------------------- 6.0/12.7 MB 6.2 MB/s eta 0:00:02\n", "   ------------------- -------------------- 6.3/12.7 MB 6.2 MB/s eta 0:00:02\n", "   -------------------- ------------------- 6.5/12.7 MB 6.2 MB/s eta 0:00:01\n", "   --------------------- ------------------ 6.9/12.7 MB 6.2 MB/s eta 0:00:01\n", "   ---------------------- ----------------- 7.1/12.7 MB 6.2 MB/s eta 0:00:01\n", "   ---------------------- ----------------- 7.3/12.7 MB 6.3 MB/s eta 0:00:01\n", "   ------------------------ --------------- 7.7/12.7 MB 6.2 MB/s eta 0:00:01\n", "   ------------------------ --------------- 7.9/12.7 MB 6.2 MB/s eta 0:00:01\n", "   -------------------------- ------------- 8.3/12.7 MB 6.2 MB/s eta 0:00:01\n", "   -------------------------- ------------- 8.6/12.7 MB 6.3 MB/s eta 0:00:01\n", "   ---------------------------- ----------- 9.0/12.7 MB 6.3 MB/s eta 0:00:01\n", "   ---------------------------- ----------- 9.1/12.7 MB 6.3 MB/s eta 0:00:01\n", "   ------------------------------ --------- 9.6/12.7 MB 6.3 MB/s eta 0:00:01\n", "   ------------------------------- -------- 10.0/12.7 MB 6.4 MB/s eta 0:00:01\n", "   -------------------------------- ------- 10.3/12.7 MB 6.4 MB/s eta 0:00:01\n", "   -------------------------------- ------- 10.3/12.7 MB 6.4 MB/s eta 0:00:01\n", "   ---------------------------------- ----- 11.1/12.7 MB 6.4 MB/s eta 0:00:01\n", "   ----------------------------------- ---- 11.3/12.7 MB 6.4 MB/s eta 0:00:01\n", "   ------------------------------------ --- 11.5/12.7 MB 6.4 MB/s eta 0:00:01\n", "   ------------------------------------- -- 11.8/12.7 MB 6.4 MB/s eta 0:00:01\n", "   ------------------------------------- -- 12.0/12.7 MB 6.3 MB/s eta 0:00:01\n", "   -------------------------------------- - 12.2/12.7 MB 6.3 MB/s eta 0:00:01\n", "   ---------------------------------------  12.7/12.7 MB 6.2 MB/s eta 0:00:01\n", "   ---------------------------------------  12.7/12.7 MB 6.2 MB/s eta 0:00:01\n", "   ---------------------------------------  12.7/12.7 MB 6.0 MB/s eta 0:00:01\n", "   ---------------------------------------- 12.7/12.7 MB 5.8 MB/s eta 0:00:00\n", "Downloading safehttpx-0.1.6-py3-none-any.whl (8.7 kB)\n", "Downloading semantic_version-2.10.0-py2.py3-none-any.whl (15 kB)\n", "Downloading tomlkit-0.13.3-py3-none-any.whl (38 kB)\n", "Downloading ffmpy-0.6.1-py3-none-any.whl (5.5 kB)\n", "Downloading pydub-0.25.1-py2.py3-none-any.whl (32 kB)\n", "Installing collected packages: pydub, brotli, tomlkit, semantic-version, ruff, python-multipart, groovy, ffmpy, huggingface-hub, safehttpx, gradio-client, gradio\n", "  Attempting uninstall: to<PERSON><PERSON><PERSON>\n", "    Found existing installation: tomlkit 0.11.1\n", "    Uninstalling tomlkit-0.11.1:\n", "      Successfully uninstalled tomlkit-0.11.1\n", "  Attempting uninstall: huggingface-hub\n", "    Found existing installation: huggingface-hub 0.29.2\n", "    Uninstalling huggingface-hub-0.29.2:\n", "      Successfully uninstalled huggingface-hub-0.29.2\n", "Successfully installed brotli-1.1.0 ffmpy-0.6.1 gradio-5.42.0 gradio-client-1.11.1 groovy-0.1.2 huggingface-hub-0.34.4 pydub-0.25.1 python-multipart-0.0.20 ruff-0.12.8 safehttpx-0.1.6 semantic-version-2.10.0 tomlkit-0.13.3\n"]}], "source": ["!pip install gradio"]}, {"cell_type": "code", "execution_count": 50, "id": "f2cbb085", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["* Running on local URL:  http://127.0.0.1:7862\n", "* To create a public link, set `share=True` in `launch()`.\n"]}, {"data": {"text/html": ["<div><iframe src=\"http://127.0.0.1:7862/\" width=\"100%\" height=\"500\" allow=\"autoplay; camera; microphone; clipboard-read; clipboard-write;\" frameborder=\"0\" allowfullscreen></iframe></div>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": []}, "execution_count": 50, "metadata": {}, "output_type": "execute_result"}], "source": ["import gradio as gr\n", "\n", "gr.ChatInterface(chat, type=\"messages\").launch()\n", "\n"]}, {"cell_type": "code", "execution_count": 52, "id": "fb6827c6", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["* Running on local URL:  http://127.0.0.1:7863\n", "* To create a public link, set `share=True` in `launch()`.\n"]}, {"data": {"text/html": ["<div><iframe src=\"http://127.0.0.1:7863/\" width=\"100%\" height=\"500\" allow=\"autoplay; camera; microphone; clipboard-read; clipboard-write;\" frameborder=\"0\" allowfullscreen></iframe></div>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": []}, "execution_count": 52, "metadata": {}, "output_type": "execute_result"}], "source": ["def greet(name):\n", "    return f\"Hello {name}!\"\n", "\n", "demo = gr.Interface(\n", "    fn=greet,                          # the function to call\n", "    inputs=gr.Textbox(label=\"Name\"),   # user input widget\n", "    outputs=gr.Textbox(label=\"Greeting\") # output display widget\n", ")\n", "\n", "demo.launch()\n"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.5"}}, "nbformat": 4, "nbformat_minor": 5}