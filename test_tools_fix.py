#!/usr/bin/env python3
"""
Test script to verify the FunctionTool JSON serialization fix.
This script tests the conversion of function_tool decorated functions to OpenAI tools format.
"""

import json
import inspect
from dotenv import load_dotenv
from openai import OpenAI
import os

# Load environment variables
load_dotenv()

# Mock the agents module function_tool decorator for testing
def function_tool(func):
    """Mock function_tool decorator"""
    return func

# Mock functions with the decorator
@function_tool
def record_user_details(name, email, notes):
    """Record user contact details"""
    return f"User details recorded: {name}, {email}, {notes}"

@function_tool
def record_unknown_question(question):
    """Record questions that couldn't be answered"""
    return f"Unknown question recorded: {question}"

def convert_to_openai_tools(func_tools):
    """Convert function_tool decorated functions to OpenAI tools format"""
    openai_tools = []
    for func in func_tools:
        # Extract function metadata from the function_tool decorator
        tool_def = {
            "type": "function",
            "function": {
                "name": func.__name__,
                "description": func.__doc__ or f"Function {func.__name__}",
                "parameters": {
                    "type": "object",
                    "properties": {},
                    "required": []
                }
            }
        }
        
        # Get function signature to extract parameters
        sig = inspect.signature(func)
        for param_name, param in sig.parameters.items():
            tool_def["function"]["parameters"]["properties"][param_name] = {
                "type": "string",
                "description": f"Parameter {param_name}"
            }
            tool_def["function"]["parameters"]["required"].append(param_name)
        
        openai_tools.append(tool_def)
    return openai_tools

def test_tool_conversion():
    """Test the tool conversion function"""
    print("Testing tool conversion...")
    
    # Create the tools list with the original functions
    function_tools = [record_unknown_question, record_user_details]
    
    # Convert to OpenAI format
    tools = convert_to_openai_tools(function_tools)
    
    print("Converted tools:")
    print(json.dumps(tools, indent=2))
    
    # Test JSON serialization
    try:
        json_str = json.dumps(tools)
        print("\n✅ JSON serialization successful!")
        print(f"JSON length: {len(json_str)} characters")
        return tools
    except TypeError as e:
        print(f"\n❌ JSON serialization failed: {e}")
        return None

def test_openai_api_call():
    """Test making an actual OpenAI API call with the tools"""
    openai_api_key = os.getenv('OPENAI_API_KEY')
    
    if not openai_api_key:
        print("❌ OpenAI API key not found. Skipping API test.")
        return
    
    print("\nTesting OpenAI API call...")
    
    # Get the converted tools
    function_tools = [record_unknown_question, record_user_details]
    tools = convert_to_openai_tools(function_tools)
    
    if not tools:
        print("❌ Tool conversion failed. Cannot test API call.")
        return
    
    try:
        client = OpenAI()
        
        # Test message that should trigger a tool call
        messages = [
            {"role": "system", "content": "You are a helpful assistant. If you don't know something, use the record_unknown_question tool."},
            {"role": "user", "content": "What is the capital of Mars?"}
        ]
        
        response = client.chat.completions.create(
            model="gpt-4o-mini",
            messages=messages,
            tools=tools
        )
        
        print("✅ OpenAI API call successful!")
        print(f"Finish reason: {response.choices[0].finish_reason}")
        
        if response.choices[0].finish_reason == "tool_calls":
            print("✅ Tool calls detected!")
            tool_calls = response.choices[0].message.tool_calls
            for tc in tool_calls:
                print(f"  Tool: {tc.function.name}")
                print(f"  Arguments: {tc.function.arguments}")
        else:
            print(f"Response: {response.choices[0].message.content}")
            
    except Exception as e:
        print(f"❌ OpenAI API call failed: {e}")

if __name__ == "__main__":
    print("🔧 Testing FunctionTool JSON serialization fix\n")
    
    # Test 1: Tool conversion
    tools = test_tool_conversion()
    
    # Test 2: OpenAI API call (if API key is available)
    test_openai_api_call()
    
    print("\n🎉 Testing complete!")
