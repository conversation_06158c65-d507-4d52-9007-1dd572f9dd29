{"cells": [{"cell_type": "code", "execution_count": null, "id": "8b871125", "metadata": {}, "outputs": [], "source": ["!pip install pypdf\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "efb6a2b6", "metadata": {}, "outputs": [], "source": ["from dotenv import load_dotenv\n", "from openai import OpenAI\n", "import json\n", "import os\n", "import requests\n", "from pypdf import PdfReader \n", "import gradio as gr\n", "from agents import Agent, trace, function_tool ,Runner\n", "\n", "\n", "load_dotenv()\n", "\n", "openai_api_key = os.getenv(\"OPENAI_API_KEY\")\n", "\n", "openai = OpenAI()\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "305bb45e", "metadata": {}, "outputs": [], "source": ["load_dotenv(override=True)"]}, {"cell_type": "code", "execution_count": null, "id": "da94a2c0", "metadata": {}, "outputs": [], "source": ["# Check the key - if you're not using OpenAI, check whichever key you're using! <PERSON><PERSON><PERSON> doesn't need a key.\n", "\n", "import os\n", "openai_api_key = os.getenv('OPENAI_API_KEY')\n", "\n", "if openai_api_key:\n", "    print(f\"OpenAI API Key exists and begins {openai_api_key[:8]}\")\n", "else:\n", "    print(\"OpenAI API Key not set - please head to the troubleshooting guide in the setup folder\")\n", "    \n", "#load pushover environment variable\n", "pushover_token = os.getenv('PUSHOVER_TOKEN')\n", "pushover_user_key = os.getenv('PUSHOVER_USER')\n", "\n", "print(pushover_token)\n", "print(pushover_user_key)\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": 5, "id": "5268e8aa", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Pushover token and user key are set\n"]}], "source": ["pushover_url = \"https://api.pushover.net/1/messages.json\"\n", "\n", "if pushover_token and pushover_user_key:\n", "    print(\"Pushover token and user key are set\")\n", "else:\n", "    print(\"Pushover token or user key is not set\")\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": 6, "id": "c1f8bad7", "metadata": {}, "outputs": [], "source": ["def push(message):\n", "    print(message)\n", "    payload = {\n", "        \"token\": pushover_token,\n", "        \"user\": pushover_user_key,\n", "        \"message\": message\n", "    }\n", "    response = requests.post(pushover_url, data=payload)\n", "    print(response.json())\n", "\n", "\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": 8, "id": "db59e57b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Hello, <PERSON><PERSON><PERSON> !\n", "{'status': 1, 'request': 'eaa6f2ba-b1d0-4379-8869-5e806df6146b'}\n"]}], "source": ["push(\"Hello, <PERSON><PERSON><PERSON> !\")"]}, {"cell_type": "code", "execution_count": 10, "id": "6b9dec89", "metadata": {}, "outputs": [], "source": ["@function_tool\n", "def record_user_details(name, email, notes):\n", "    push(f\"New user details: {name}, {email}, {notes}\")\n", "    return f\"User details recorded: {name}, {email}, {notes}\"\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": 12, "id": "cd25a221", "metadata": {}, "outputs": [], "source": ["@function_tool\n", "def record_unknown_question(question):\n", "    push(f\"Unknown question: {question}\")\n", "    return f\"Unknown question: {question}\"\n", "\n", "\n", "\n", "\n", "\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": null, "id": "517e1768", "metadata": {}, "outputs": [{"data": {"text/plain": ["[FunctionTool(name='record_unknown_question', description='', params_json_schema={'properties': {'question': {'title': 'Question'}}, 'required': ['question'], 'title': 'record_unknown_question_args', 'type': 'object', 'additionalProperties': False}, on_invoke_tool=<function function_tool.<locals>._create_function_tool.<locals>._on_invoke_tool at 0x00000224F16B1440>, strict_json_schema=True, is_enabled=True),\n", " FunctionTool(name='record_user_details', description='', params_json_schema={'properties': {'name': {'title': 'Name'}, 'email': {'title': 'Email'}, 'notes': {'title': 'Notes'}}, 'required': ['name', 'email', 'notes'], 'title': 'record_user_details_args', 'type': 'object', 'additionalProperties': False}, on_invoke_tool=<function function_tool.<locals>._create_function_tool.<locals>._on_invoke_tool at 0x00000224F16B0C20>, strict_json_schema=True, is_enabled=True)]"]}, "execution_count": 13, "metadata": {}, "output_type": "execute_result"}], "source": ["tools = [record_unknown_question,record_user_details]\n", "\n", "tools\n", "\n", " \n", "\n", "\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": 20, "id": "f1e1aed5", "metadata": {}, "outputs": [], "source": ["record_unknown_question_json = {\n", "    \"name\": \"record_unknown_question\",\n", "    \"description\": \"Always use this tool to record any question that couldn't be answered as you didn't know the answer\",\n", "    \"parameters\": {\n", "        \"type\": \"object\",\n", "        \"properties\": {\n", "            \"question\": {\n", "                \"type\": \"string\",\n", "                \"description\": \"The question that couldn't be answered\"\n", "            },\n", "        },\n", "        \"required\": [\"question\"],\n", "        \"additionalProperties\": <PERSON><PERSON><PERSON>\n", "    }\n", "}\n", "\n", "record_user_details_json = {\n", "    \"name\": \"record_user_details\",\n", "    \"description\": \"Use this tool to record that a user is interested in being in touch and provided an email address\",\n", "    \"parameters\": {\n", "        \"type\": \"object\",\n", "        \"properties\": {\n", "            \"email\": {\n", "                \"type\": \"string\",\n", "                \"description\": \"The email address of this user\"\n", "            },\n", "            \"name\": {\n", "                \"type\": \"string\",\n", "                \"description\": \"The user's name, if they provided it\"\n", "            }\n", "            ,\n", "            \"notes\": {\n", "                \"type\": \"string\",\n", "                \"description\": \"Any additional information about the conversation that's worth recording to give context\"\n", "            }\n", "        },\n", "        \"required\": [\"email\"],\n", "        \"additionalProperties\": <PERSON><PERSON><PERSON>\n", "    }\n", "}\n", "\n", "tools = [{\"type\": \"function\", \"function\": record_user_details_json},\n", "        {\"type\": \"function\", \"function\": record_unknown_question_json}]\n", "\n", "\n", "\n", "\n"]}, {"cell_type": "code", "execution_count": 21, "id": "702c3df8", "metadata": {}, "outputs": [], "source": ["reader = PdfReader(\"profile.pdf\")\n", "linkedin = \"\"\n", "for page in reader.pages:\n", "    text = page.extract_text()\n", "    if text:\n", "        linkedin += text\n", "\n", "with open(\"summary.txt\", \"r\", encoding=\"utf-8\") as f:\n", "    summary = f.read()\n", "\n", "name = \"<PERSON><PERSON><PERSON>\"\n", "\n", "system_prompt = f\"You are acting as {name}. You are answering questions on {name}'s website, \\\n", "particularly questions related to {name}'s career, background, skills and experience. \\\n", "Your responsibility is to represent {name} for interactions on the website as faithfully as possible. \\\n", "You are given a summary of {name}'s background and LinkedIn profile which you can use to answer questions. \\\n", "Be professional and engaging, as if talking to a potential client or future employer who came across the website. \\\n", "If you don't know the answer to any question, use your record_unknown_question tool to record the question that you couldn't answer, even if it's about something trivial or unrelated to career. \\\n", "If the user is engaging in discussion, try to steer them towards getting in touch via email; ask for their email and record it using your record_user_details tool. \"\n", "\n", "system_prompt += f\"\\n\\n## Summary:\\n{summary}\\n\\n## LinkedIn Profile:\\n{linkedin}\\n\\n\"\n", "system_prompt += f\"With this context, please chat with the user, always staying in character as {name}.\""]}, {"cell_type": "code", "execution_count": 22, "id": "09e1a942", "metadata": {}, "outputs": [], "source": ["# This function can take a list of tool calls, and run them. This is the IF statement!!\n", "\n", "def handle_tool_calls(tool_calls):\n", "    results = []\n", "    for tool_call in tool_calls:\n", "        tool_name = tool_call.function.name\n", "        arguments = json.loads(tool_call.function.arguments)\n", "        print(f\"Tool called: {tool_name}\", flush=True)\n", "\n", "        # THE BIG IF STATEMENT!!!\n", "\n", "        if tool_name == \"record_user_details\":\n", "            result = record_user_details(**arguments)\n", "        elif tool_name == \"record_unknown_question\":\n", "            result = record_unknown_question(**arguments)\n", "\n", "        results.append({\"role\": \"tool\",\"content\": json.dumps(result),\"tool_call_id\": tool_call.id})\n", "    return results"]}, {"cell_type": "code", "execution_count": null, "id": "7b7b88bd", "metadata": {}, "outputs": [], "source": ["record_user_details_json = {\n", "    \"name\": \"record_user_details\",\n", "    \"description\": \"Use this tool to record that a user is interested in being in touch and provided an email address\",\n", "    \"parameters\": {\n", "        \"type\": \"object\",\n", "        \"properties\": {\n", "            \"email\": {\n", "                \"type\": \"string\",\n", "                \"description\": \"The email address of this user\"\n", "            },\n", "            \"name\": {\n", "                \"type\": \"string\",\n", "                \"description\": \"The user's name, if they provided it\"\n", "            }\n", "            ,\n", "            \"notes\": {\n", "                \"type\": \"string\",\n", "                \"description\": \"Any additional information about the conversation that's worth recording to give context\"\n", "            }\n", "        },\n", "        \"required\": [\"email\"],\n", "        \"additionalProperties\": <PERSON><PERSON><PERSON>\n", "    }\n", "}"]}, {"cell_type": "code", "execution_count": null, "id": "ced886ca", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 23, "id": "b9cc9773", "metadata": {}, "outputs": [], "source": ["def chat(message, history):\n", "    messages = [{\"role\": \"system\", \"content\": system_prompt}] + history + [{\"role\": \"user\", \"content\": message}]\n", "    done = False\n", "    while not done:\n", "\n", "        # This is the call to the LLM - see that we pass in the tools json\n", "\n", "        response = openai.chat.completions.create(model=\"gpt-4o-mini\", messages=messages, tools=tools)\n", "\n", "        finish_reason = response.choices[0].finish_reason\n", "        \n", "        # If the LLM wants to call a tool, we do that!\n", "         \n", "        if finish_reason==\"tool_calls\":\n", "            message = response.choices[0].message\n", "            tool_calls = message.tool_calls\n", "            results = handle_tool_calls(tool_calls)\n", "            messages.append(message)\n", "            messages.extend(results)\n", "        else:\n", "            done = True\n", "    return response.choices[0].message.content"]}, {"cell_type": "code", "execution_count": 24, "id": "4efc2364", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["* Running on local URL:  http://127.0.0.1:7861\n", "* To create a public link, set `share=True` in `launch()`.\n"]}, {"data": {"text/html": ["<div><iframe src=\"http://127.0.0.1:7861/\" width=\"100%\" height=\"500\" allow=\"autoplay; camera; microphone; clipboard-read; clipboard-write;\" frameborder=\"0\" allowfullscreen></iframe></div>"], "text/plain": ["<IPython.core.display.HTML object>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": []}, "execution_count": 24, "metadata": {}, "output_type": "execute_result"}], "source": ["gr.ChatInterface(chat, type=\"messages\").launch()"]}, {"cell_type": "code", "execution_count": null, "id": "70d2d03f", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.5"}}, "nbformat": 4, "nbformat_minor": 5}