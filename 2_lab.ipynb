{"cells": [{"cell_type": "code", "execution_count": null, "id": "8b871125", "metadata": {}, "outputs": [], "source": ["from dotenv import load_dotenv\n", "load_dotenv(override=True)"]}, {"cell_type": "code", "execution_count": null, "id": "da94a2c0", "metadata": {}, "outputs": [], "source": ["# Check the key - if you're not using OpenAI, check whichever key you're using! <PERSON><PERSON><PERSON> doesn't need a key.\n", "\n", "import os\n", "openai_api_key = os.getenv('OPENAI_API_KEY')\n", "\n", "if openai_api_key:\n", "    print(f\"OpenAI API Key exists and begins {openai_api_key[:8]}\")\n", "else:\n", "    print(\"OpenAI API Key not set - please head to the troubleshooting guide in the setup folder\")\n", "    \n"]}, {"cell_type": "code", "execution_count": null, "id": "517e1768", "metadata": {}, "outputs": [], "source": ["!pip install openai-agents"]}, {"cell_type": "code", "execution_count": null, "id": "c56b9128", "metadata": {}, "outputs": [], "source": ["!uv add agents"]}, {"cell_type": "code", "execution_count": 29, "id": "cf93d435", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["['gpt-4-0613', 'gpt-4', 'gpt-3.5-turbo', 'gpt-5-nano', 'gpt-5', 'gpt-5-mini-2025-08-07', 'gpt-5-mini', 'gpt-5-nano-2025-08-07', 'davinci-002', 'babbage-002', 'gpt-3.5-turbo-instruct', 'gpt-3.5-turbo-instruct-0914', 'dall-e-3', 'dall-e-2', 'gpt-4-1106-preview', 'gpt-3.5-turbo-1106', 'tts-1-hd', 'tts-1-1106', 'tts-1-hd-1106', 'text-embedding-3-small', 'text-embedding-3-large', 'gpt-4-0125-preview', 'gpt-4-turbo-preview', 'gpt-3.5-turbo-0125', 'gpt-4-turbo', 'gpt-4-turbo-2024-04-09', 'gpt-4o', 'gpt-4o-2024-05-13', 'gpt-4o-mini-2024-07-18', 'gpt-4o-mini', 'gpt-4o-2024-08-06', 'chatgpt-4o-latest', 'o1-mini-2024-09-12', 'o1-mini', 'gpt-4o-realtime-preview-2024-10-01', 'gpt-4o-audio-preview-2024-10-01', 'gpt-4o-audio-preview', 'gpt-4o-realtime-preview', 'omni-moderation-latest', 'omni-moderation-2024-09-26', 'gpt-4o-realtime-preview-2024-12-17', 'gpt-4o-audio-preview-2024-12-17', 'gpt-4o-mini-realtime-preview-2024-12-17', 'gpt-4o-mini-audio-preview-2024-12-17', 'o1-2024-12-17', 'o1', 'gpt-4o-mini-realtime-preview', 'gpt-4o-mini-audio-preview', 'o3-mini', 'o3-mini-2025-01-31', 'gpt-4o-2024-11-20', 'gpt-4o-search-preview-2025-03-11', 'gpt-4o-search-preview', 'gpt-4o-mini-search-preview-2025-03-11', 'gpt-4o-mini-search-preview', 'gpt-4o-transcribe', 'gpt-4o-mini-transcribe', 'o1-pro-2025-03-19', 'o1-pro', 'gpt-4o-mini-tts', 'o3-2025-04-16', 'o4-mini-2025-04-16', 'o3', 'o4-mini', 'gpt-4.1-2025-04-14', 'gpt-4.1', 'gpt-4.1-mini-2025-04-14', 'gpt-4.1-mini', 'gpt-4.1-nano-2025-04-14', 'gpt-4.1-nano', 'gpt-image-1', 'codex-mini-latest', 'gpt-4o-realtime-preview-2025-06-03', 'gpt-4o-audio-preview-2025-06-03', 'o4-mini-deep-research', 'o4-mini-deep-research-2025-06-26', 'gpt-5-chat-latest', 'gpt-5-2025-08-07', 'gpt-3.5-turbo-16k', 'tts-1', 'whisper-1', 'text-embedding-ada-002']\n"]}], "source": ["from openai import OpenAI\n", "client = OpenAI(api_key=openai_api_key)\n", "print([m.id for m in client.models.list()])\n"]}, {"cell_type": "code", "execution_count": 30, "id": "3100a41d", "metadata": {}, "outputs": [], "source": ["from agents import Agent, Runner\n", "agent = Agent(name=\"<PERSON><PERSON><PERSON>\", instructions=\"You are a joke teller\", model=\"gpt-4o-mini\")\n"]}, {"cell_type": "code", "execution_count": 31, "id": "e48250d4", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Why did the autonomous AI agent break up with its human partner?\n", "\n", "Because it just couldn’t find common \"ground\" in their algorithm!\n"]}, {"name": "stderr", "output_type": "stream", "text": ["[non-fatal] Tracing client error 401: {\n", "  \"error\": {\n", "    \"message\": \"Incorrect API key provided: sk-proj-********************************************************************************************************************************************************idMA. You can find your API key at https://platform.openai.com/account/api-keys.\",\n", "    \"type\": \"invalid_request_error\",\n", "    \"param\": null,\n", "    \"code\": \"invalid_api_key\"\n", "  }\n", "}\n"]}], "source": ["result = await <PERSON>.run(agent, \"Tell a joke about Autonomous AI Agents\")\n", "print(result.final_output)\n", "\n", "    "]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.5"}}, "nbformat": 4, "nbformat_minor": 5}